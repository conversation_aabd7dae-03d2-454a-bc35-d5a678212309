/**
 * Centralized Email Translation Management System
 * Provides consistent translations across all email templates
 */

export type SupportedLanguage = 'zh' | 'en';

export interface EmailTranslations {
  common: {
    brandName: string;
    greeting: string;
    regards: string;
    team: string;
    autoMessage: string;
    copyright: string;
    supportEmail: string;
    websiteUrl: string;
  };
  notifications: {
    newUser: {
      title: string;
      subject: string;
      header: string;
      description: string;
      userLabel: string;
      emailLabel: string;
      timeLabel: string;
    };
    verification: {
      register: {
        title: string;
        subject: string;
        description: string;
      };
      login: {
        title: string;
        subject: string;
        description: string;
      };
      resetPassword: {
        title: string;
        subject: string;
        description: string;
      };
      verifyEmail: {
        title: string;
        subject: string;
        description: string;
      };
      changeEmail: {
        title: string;
        subject: string;
        description: string;
      };
      withdrawalVerification: {
        title: string;
        subject: string;
        description: string;
      };
      codeLabel: string;
      expiresLabel: string;
      securityWarning: string;
      securityTips: string[];
    };
    financial: {
      depositSuccess: {
        title: string;
        subject: string;
        header: string;
        description: string;
        amountLabel: string;
        transactionIdLabel: string;
        paymentMethodLabel: string;
        newBalanceLabel: string;
        processedAtLabel: string;
      };
      depositFailed: {
        title: string;
        subject: string;
        header: string;
        description: string;
        amountLabel: string;
        reasonLabel: string;
        supportMessage: string;
      };
      withdrawalApproved: {
        title: string;
        subject: string;
        header: string;
        description: string;
        amountLabel: string;
        processingTime: string;
      };
      withdrawalRejected: {
        title: string;
        subject: string;
        header: string;
        description: string;
        reasonLabel: string;
        contactSupport: string;
      };
    };
    tasks: {
      completed: {
        publisher: {
          title: string;
          subject: string;
          header: string;
          description: string;
        };
        accepter: {
          title: string;
          subject: string;
          header: string;
          description: string;
        };
      };
      cancelled: {
        publisher: {
          title: string;
          subject: string;
          header: string;
          description: string;
        };
        accepter: {
          title: string;
          subject: string;
          header: string;
          description: string;
        };
      };
      accepted: {
        publisher: {
          title: string;
          subject: string;
          header: string;
          description: string;
        };
      };
    };
  };
  ui: {
    buttons: {
      viewDetails: string;
      contactSupport: string;
      visitWebsite: string;
      checkAccount: string;
    };
    labels: {
      important: string;
      warning: string;
      success: string;
      info: string;
      minutes: string;
      hours: string;
      days: string;
    };
  };
}

export const emailTranslations: Record<SupportedLanguage, EmailTranslations> = {
  zh: {
    common: {
      brandName: 'RefundGo',
      greeting: '您好',
      regards: '此致敬礼',
      team: 'RefundGo 团队',
      autoMessage: '此邮件由 RefundGo 系统自动发送，请勿回复。',
      copyright: '© 2024 RefundGo. 保留所有权利。',
      supportEmail: '<EMAIL>',
      websiteUrl: 'https://refundgo.org',
    },
    notifications: {
      newUser: {
        title: '新用户注册通知',
        subject: '新用户注册通知 - RefundGo',
        header: '新用户注册通知',
        description: '有新用户注册了账户',
        userLabel: '用户：',
        emailLabel: '邮箱：',
        timeLabel: '时间：',
      },
      verification: {
        register: {
          title: '注册验证码',
          subject: '注册验证码 - RefundGo',
          description: '欢迎加入RefundGo！请使用以下验证码完成注册',
        },
        login: {
          title: '登录验证码',
          subject: '登录验证码 - RefundGo',
          description: '请使用以下验证码完成登录',
        },
        resetPassword: {
          title: '重置密码验证码',
          subject: '重置密码验证码 - RefundGo',
          description: '请使用以下验证码重置密码',
        },
        verifyEmail: {
          title: '邮箱验证码',
          subject: '邮箱验证码 - RefundGo',
          description: '请使用以下验证码验证您的邮箱',
        },
        changeEmail: {
          title: '更换邮箱验证码',
          subject: '更换邮箱验证码 - RefundGo',
          description: '请使用以下验证码完成邮箱更换',
        },
        withdrawalVerification: {
          title: '提现验证码',
          subject: '提现验证码 - RefundGo',
          description: '请使用以下验证码完成提现验证',
        },
        codeLabel: '验证码',
        expiresLabel: '有效期',
        securityWarning: '重要提醒',
        securityTips: [
          '验证码有效期为 {minutes} 分钟',
          '请勿将验证码告诉他人',
          '如非本人操作，请忽略此邮件',
        ],
      },
      financial: {
        depositSuccess: {
          title: '充值成功确认',
          subject: '充值成功确认 - RefundGo',
          header: '充值成功！',
          description: '您的充值已成功完成',
          amountLabel: '充值金额：',
          transactionIdLabel: '交易编号：',
          paymentMethodLabel: '支付方式：',
          newBalanceLabel: '账户余额：',
          processedAtLabel: '处理时间：',
        },
        depositFailed: {
          title: '充值失败通知',
          subject: '充值失败通知 - RefundGo',
          header: '充值失败',
          description: '很抱歉，您的充值未能成功完成',
          amountLabel: '充值金额：',
          reasonLabel: '失败原因：',
          supportMessage: '如需帮助，请联系客服支持',
        },
        withdrawalApproved: {
          title: '提现申请已批准',
          subject: '提现申请已批准 - RefundGo',
          header: '提现申请已批准',
          description: '您的提现申请已通过审核',
          amountLabel: '提现金额：',
          processingTime: '预计到账时间：1-3个工作日',
        },
        withdrawalRejected: {
          title: '提现申请被拒绝',
          subject: '提现申请被拒绝 - RefundGo',
          header: '提现申请被拒绝',
          description: '很抱歉，您的提现申请未能通过审核',
          reasonLabel: '拒绝原因：',
          contactSupport: '如有疑问，请联系客服支持',
        },
      },
      tasks: {
        completed: {
          publisher: {
            title: '任务完成通知（发布者）',
            subject: '任务完成通知 - RefundGo',
            header: '任务已完成',
            description: '您发布的任务已成功完成',
          },
          accepter: {
            title: '任务完成通知（接受者）',
            subject: '任务完成通知 - RefundGo',
            header: '任务已完成',
            description: '您接受的任务已成功完成',
          },
        },
        cancelled: {
          publisher: {
            title: '任务取消通知（发布者）',
            subject: '任务取消通知 - RefundGo',
            header: '任务已取消',
            description: '您发布的任务已被取消',
          },
          accepter: {
            title: '任务取消通知（接受者）',
            subject: '任务取消通知 - RefundGo',
            header: '任务已取消',
            description: '您接受的任务已被取消',
          },
        },
        accepted: {
          publisher: {
            title: '任务被接受通知',
            subject: '任务被接受通知 - RefundGo',
            header: '任务已被接受',
            description: '您发布的任务已被用户接受',
          },
        },
      },
    },
    ui: {
      buttons: {
        viewDetails: '查看详情',
        contactSupport: '联系客服',
        visitWebsite: '访问网站',
        checkAccount: '查看账户',
      },
      labels: {
        important: '重要',
        warning: '警告',
        success: '成功',
        info: '信息',
        minutes: '分钟',
        hours: '小时',
        days: '天',
      },
    },
  },
  en: {
    common: {
      brandName: 'RefundGo',
      greeting: 'Hello',
      regards: 'Best regards',
      team: 'RefundGo Team',
      autoMessage: 'This email was sent automatically by RefundGo system. Please do not reply.',
      copyright: '© 2024 RefundGo. All rights reserved.',
      supportEmail: '<EMAIL>',
      websiteUrl: 'https://refundgo.org',
    },
    notifications: {
      newUser: {
        title: 'New User Registration Notification',
        subject: 'New User Registration Notification - RefundGo',
        header: 'New User Registration',
        description: 'A new user has registered an account',
        userLabel: 'User:',
        emailLabel: 'Email:',
        timeLabel: 'Time:',
      },
      verification: {
        register: {
          title: 'Registration Verification Code',
          subject: 'Registration Verification Code - RefundGo',
          description: 'Welcome to RefundGo! Please use the following verification code to complete registration',
        },
        login: {
          title: 'Login Verification Code',
          subject: 'Login Verification Code - RefundGo',
          description: 'Please use the following verification code to complete login',
        },
        resetPassword: {
          title: 'Password Reset Verification Code',
          subject: 'Password Reset Verification Code - RefundGo',
          description: 'Please use the following verification code to reset your password',
        },
        verifyEmail: {
          title: 'Email Verification Code',
          subject: 'Email Verification Code - RefundGo',
          description: 'Please use the following verification code to verify your email',
        },
        changeEmail: {
          title: 'Email Change Verification Code',
          subject: 'Email Change Verification Code - RefundGo',
          description: 'Please use the following verification code to complete email change',
        },
        withdrawalVerification: {
          title: 'Withdrawal Verification Code',
          subject: 'Withdrawal Verification Code - RefundGo',
          description: 'Please use the following verification code to complete withdrawal verification',
        },
        codeLabel: 'Verification Code',
        expiresLabel: 'Valid for',
        securityWarning: 'Important Notice',
        securityTips: [
          'Verification code is valid for {minutes} minutes',
          'Do not share this code with anyone',
          'If you did not request this, please ignore this email',
        ],
      },
      financial: {
        depositSuccess: {
          title: 'Deposit Success Confirmation',
          subject: 'Deposit Success Confirmation - RefundGo',
          header: 'Deposit Successful!',
          description: 'Your deposit has been successfully completed',
          amountLabel: 'Deposit Amount:',
          transactionIdLabel: 'Transaction ID:',
          paymentMethodLabel: 'Payment Method:',
          newBalanceLabel: 'Account Balance:',
          processedAtLabel: 'Processed At:',
        },
        depositFailed: {
          title: 'Deposit Failed Notification',
          subject: 'Deposit Failed Notification - RefundGo',
          header: 'Deposit Failed',
          description: 'We apologize, but your deposit could not be completed successfully',
          amountLabel: 'Deposit Amount:',
          reasonLabel: 'Failure Reason:',
          supportMessage: 'If you need assistance, please contact customer support',
        },
        withdrawalApproved: {
          title: 'Withdrawal Request Approved',
          subject: 'Withdrawal Request Approved - RefundGo',
          header: 'Withdrawal Request Approved',
          description: 'Your withdrawal request has been approved',
          amountLabel: 'Withdrawal Amount:',
          processingTime: 'Expected processing time: 1-3 business days',
        },
        withdrawalRejected: {
          title: 'Withdrawal Request Rejected',
          subject: 'Withdrawal Request Rejected - RefundGo',
          header: 'Withdrawal Request Rejected',
          description: 'We apologize, but your withdrawal request could not be approved',
          reasonLabel: 'Rejection Reason:',
          contactSupport: 'If you have questions, please contact customer support',
        },
      },
      tasks: {
        completed: {
          publisher: {
            title: 'Task Completion Notification (Publisher)',
            subject: 'Task Completion Notification - RefundGo',
            header: 'Task Completed',
            description: 'Your published task has been successfully completed',
          },
          accepter: {
            title: 'Task Completion Notification (Accepter)',
            subject: 'Task Completion Notification - RefundGo',
            header: 'Task Completed',
            description: 'Your accepted task has been successfully completed',
          },
        },
        cancelled: {
          publisher: {
            title: 'Task Cancellation Notification (Publisher)',
            subject: 'Task Cancellation Notification - RefundGo',
            header: 'Task Cancelled',
            description: 'Your published task has been cancelled',
          },
          accepter: {
            title: 'Task Cancellation Notification (Accepter)',
            subject: 'Task Cancellation Notification - RefundGo',
            header: 'Task Cancelled',
            description: 'Your accepted task has been cancelled',
          },
        },
        accepted: {
          publisher: {
            title: 'Task Accepted Notification',
            subject: 'Task Accepted Notification - RefundGo',
            header: 'Task Accepted',
            description: 'Your published task has been accepted by a user',
          },
        },
      },
    },
    ui: {
      buttons: {
        viewDetails: 'View Details',
        contactSupport: 'Contact Support',
        visitWebsite: 'Visit Website',
        checkAccount: 'Check Account',
      },
      labels: {
        important: 'Important',
        warning: 'Warning',
        success: 'Success',
        info: 'Info',
        minutes: 'minutes',
        hours: 'hours',
        days: 'days',
      },
    },
  },
};

/**
 * Get translations for a specific language
 */
export function getEmailTranslations(language: SupportedLanguage): EmailTranslations {
  return emailTranslations[language] || emailTranslations.zh;
}

/**
 * Format translation strings with variables
 */
export function formatEmailTranslation(
  template: string,
  variables: Record<string, string | number>,
): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key]?.toString() || match;
  });
}
